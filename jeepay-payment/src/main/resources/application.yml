server:
  port: 9216 #设置端口

spring:
  data:
    redis:
      database: 3    #1库：运营平台  #2库：商户系统 #3库：支付网关

# knife4j APIDOC文档
springdoc:
  swagger-ui:
    path: /swagger-ui.html
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      display-name: '支付网关'
      paths-to-match: '/**'
      packages-to-scan: com.jeequan.jeepay.pay.ctrl.test
knife4j:
  enable: true
  setting:
    language: zh_cn
    swagger-model-name: 公共响应
  documents:
    - name: API
      locations: classpath:markdown/doc/*
      group: default
