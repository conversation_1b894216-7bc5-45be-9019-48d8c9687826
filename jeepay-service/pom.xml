<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> <!-- POM模型版本 -->

  <groupId>com.jeequan</groupId> <!-- 组织名, 类似于包名 -->
  <artifactId>jeepay-service</artifactId>  <!-- 项目名称 -->
  <packaging>jar</packaging> <!-- 项目的最终打包类型/发布形式, 可选[jar, war, pom, maven-plugin]等 -->
  <version>${isys.version}</version> <!-- 项目当前版本号 -->
  <description>四方支付系统 [jeepay-service]</description> <!-- 项目描述 -->
  <url>https://www.jeequan.com</url>

  <parent>
    <groupId>com.jeequan</groupId>
    <artifactId>jeepay</artifactId>
    <version>Final</version>
  </parent>

  <!-- 项目依赖声明 -->
  <dependencies>

    <dependency>
      <groupId>com.jeequan</groupId>
      <artifactId>jeepay-core</artifactId>
    </dependency>

    <!-- MySql 数据库连接包 -->
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>

    <!-- Druid 连接池 -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>1.2.6</version>
    </dependency>

    <!-- orm映射框架：mybatis-plus,  自动引入spring-tx -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
    </dependency>

    <!-- Spring Security -->
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-core</artifactId>
      <scope>provided</scope>
    </dependency>

  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <directory>src/main/java</directory>
        <includes><include>**/*.xml</include></includes><!-- maven可以将mapper.xml进行打包处理，否则仅对java文件处理 -->
      </resource>
    </resources>

  </build>

</project>
