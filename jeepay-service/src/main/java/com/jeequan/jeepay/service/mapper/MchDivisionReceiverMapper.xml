<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeequan.jeepay.service.mapper.MchDivisionReceiverMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jeequan.jeepay.core.entity.MchDivisionReceiver">
        <id column="receiver_id" property="receiverId" />
        <result column="receiver_alias" property="receiverAlias" />
        <result column="receiver_group_id" property="receiverGroupId" />
        <result column="receiver_group_name" property="receiverGroupName" />
        <result column="mch_no" property="mchNo" />
        <result column="isv_no" property="isvNo" />
        <result column="app_id" property="appId" />
        <result column="if_code" property="ifCode" />
        <result column="acc_type" property="accType" />
        <result column="acc_no" property="accNo" />
        <result column="acc_name" property="accName" />
        <result column="relation_type" property="relationType" />
        <result column="relation_type_name" property="relationTypeName" />
        <result column="division_profit" property="divisionProfit" />
        <result column="state" property="state" />
        <result column="channel_bind_result" property="channelBindResult" />
        <result column="channel_ext_info" property="channelExtInfo" />
        <result column="bind_success_time" property="bindSuccessTime" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

</mapper>
