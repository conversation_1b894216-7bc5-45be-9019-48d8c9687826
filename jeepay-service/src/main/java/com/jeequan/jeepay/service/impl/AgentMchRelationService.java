/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeequan.jeepay.core.entity.AgentMchRelation;
import com.jeequan.jeepay.service.mapper.AgentMchRelationMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 代理商商户关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AgentMchRelationService extends ServiceImpl<AgentMchRelationMapper, AgentMchRelation> {

    /**
     * 根据代理商号查询关联的商户号列表
     * @param agentNo 代理商号
     * @return 商户号列表
     */
    public List<String> getMchNosByAgentNo(String agentNo) {
        return baseMapper.selectMchNosByAgentNo(agentNo);
    }

    /**
     * 根据商户号查询关联的代理商号列表
     * @param mchNo 商户号
     * @return 代理商号列表
     */
    public List<String> getAgentNosByMchNo(String mchNo) {
        return baseMapper.selectAgentNosByMchNo(mchNo);
    }

    /**
     * 分页查询代理商商户关系
     * @param page 分页参数
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param relationType 关系类型
     * @return 关系分页数据
     */
    public IPage<AgentMchRelation> selectPage(IPage<AgentMchRelation> page, String agentNo, String mchNo, Byte relationType) {
        LambdaQueryWrapper<AgentMchRelation> wrapper = AgentMchRelation.gw();
        
        if (StringUtils.isNotBlank(agentNo)) {
            wrapper.eq(AgentMchRelation::getAgentNo, agentNo);
        }
        if (StringUtils.isNotBlank(mchNo)) {
            wrapper.like(AgentMchRelation::getMchNo, mchNo);
        }
        if (relationType != null) {
            wrapper.eq(AgentMchRelation::getRelationType, relationType);
        }
        
        wrapper.orderByDesc(AgentMchRelation::getCreatedAt);
        return page(page, wrapper);
    }

    /**
     * 创建代理商商户关系
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param relationType 关系类型
     * @param profitRate 分润比例
     * @param createdUid 创建者用户ID
     * @param createdBy 创建者姓名
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRelation(String agentNo, String mchNo, Byte relationType, 
                                 java.math.BigDecimal profitRate, Long createdUid, String createdBy) {
        // 检查关系是否已存在
        AgentMchRelation existRelation = getOne(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));
        
        if (existRelation != null) {
            return false; // 关系已存在
        }
        
        AgentMchRelation relation = new AgentMchRelation();
        relation.setAgentNo(agentNo);
        relation.setMchNo(mchNo);
        relation.setRelationType(relationType);
        relation.setProfitRate(profitRate);
        relation.setState(AgentMchRelation.STATE_NORMAL);
        relation.setCreatedUid(createdUid);
        relation.setCreatedBy(createdBy);
        
        return save(relation);
    }

    /**
     * 更新分润比例
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param profitRate 新的分润比例
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProfitRate(String agentNo, String mchNo, java.math.BigDecimal profitRate) {
        AgentMchRelation relation = getOne(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));
        
        if (relation == null) {
            return false;
        }
        
        relation.setProfitRate(profitRate);
        return updateById(relation);
    }

    /**
     * 删除代理商商户关系
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRelation(String agentNo, String mchNo) {
        return remove(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));
    }

    /**
     * 批量创建代理商商户关系
     * @param relations 关系列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateRelations(List<AgentMchRelation> relations) {
        if (relations == null || relations.isEmpty()) {
            return true;
        }
        
        return baseMapper.batchInsert(relations) > 0;
    }

    /**
     * 根据代理商号删除所有关系
     * @param agentNo 代理商号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByAgentNo(String agentNo) {
        return remove(AgentMchRelation.gw().eq(AgentMchRelation::getAgentNo, agentNo));
    }

    /**
     * 根据商户号删除所有关系
     * @param mchNo 商户号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByMchNo(String mchNo) {
        return remove(AgentMchRelation.gw().eq(AgentMchRelation::getMchNo, mchNo));
    }

    /**
     * 检查代理商是否有关联商户
     * @param agentNo 代理商号
     * @return 是否有关联商户
     */
    public boolean hasRelatedMerchants(String agentNo) {
        return count(AgentMchRelation.gw().eq(AgentMchRelation::getAgentNo, agentNo)) > 0;
    }

    /**
     * 获取代理商商户关系
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @return 关系信息
     */
    public AgentMchRelation getRelation(String agentNo, String mchNo) {
        return getOne(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));
    }

}
