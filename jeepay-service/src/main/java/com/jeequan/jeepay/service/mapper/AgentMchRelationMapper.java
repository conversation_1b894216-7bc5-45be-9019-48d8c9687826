/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeequan.jeepay.core.entity.AgentMchRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 代理商商户关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AgentMchRelationMapper extends BaseMapper<AgentMchRelation> {

    /**
     * 根据代理商号查询所有关联商户
     * @param agentNo 代理商号
     * @return 关联商户列表
     */
    List<String> selectMchNosByAgentNo(@Param("agentNo") String agentNo);

    /**
     * 根据商户号查询所有关联代理商
     * @param mchNo 商户号
     * @return 关联代理商列表
     */
    List<String> selectAgentNosByMchNo(@Param("mchNo") String mchNo);

    /**
     * 批量插入代理商商户关系
     * @param relations 关系列表
     * @return 插入行数
     */
    int batchInsert(@Param("relations") List<AgentMchRelation> relations);

}
