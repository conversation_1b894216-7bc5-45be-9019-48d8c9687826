/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeequan.jeepay.core.entity.AgentProfitRecord;
import com.jeequan.jeepay.service.mapper.AgentProfitRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 代理商分润记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AgentProfitRecordService extends ServiceImpl<AgentProfitRecordMapper, AgentProfitRecord> {

    /**
     * 分页查询代理商分润记录
     * @param page 分页参数
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param state 分润状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分润记录分页数据
     */
    public IPage<AgentProfitRecord> selectProfitRecordPage(Page<AgentProfitRecord> page,
                                                          String agentNo, String mchNo, Byte state,
                                                          Date startDate, Date endDate) {
        return baseMapper.selectProfitRecordPage(page, agentNo, mchNo, state, startDate, endDate);
    }

    /**
     * 统计代理商分润金额
     * @param agentNo 代理商号
     * @param state 分润状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分润金额统计
     */
    public Map<String, Object> sumProfitAmount(String agentNo, Byte state, Date startDate, Date endDate) {
        return baseMapper.sumProfitAmount(agentNo, state, startDate, endDate);
    }

    /**
     * 创建分润记录
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param payOrderId 支付订单号
     * @param orderAmount 订单金额
     * @param mchFeeAmount 商户手续费
     * @param profitRate 分润比例
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createProfitRecord(String agentNo, String mchNo, String payOrderId,
                                     Long orderAmount, Long mchFeeAmount, BigDecimal profitRate) {
        // 计算分润金额（基于商户手续费）
        Long profitAmount = mchFeeAmount * profitRate.longValue() / 100;
        
        AgentProfitRecord record = new AgentProfitRecord();
        record.setAgentNo(agentNo);
        record.setMchNo(mchNo);
        record.setPayOrderId(payOrderId);
        record.setOrderAmount(orderAmount);
        record.setMchFeeAmount(mchFeeAmount);
        record.setProfitRate(profitRate);
        record.setProfitAmount(profitAmount);
        record.setProfitDate(new Date());
        record.setState(AgentProfitRecord.STATE_WAIT_SETTLE);
        
        return save(record);
    }

    /**
     * 批量创建分润记录
     * @param records 分润记录列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateProfitRecords(List<AgentProfitRecord> records) {
        if (records == null || records.isEmpty()) {
            return true;
        }
        
        return baseMapper.batchInsert(records) > 0;
    }

    /**
     * 批量结算分润记录
     * @param ids 记录ID列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSettleProfitRecords(List<Long> ids) {
        return baseMapper.batchUpdateState(ids, AgentProfitRecord.STATE_SETTLED, new Date()) > 0;
    }

    /**
     * 批量取消分润记录
     * @param ids 记录ID列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCancelProfitRecords(List<Long> ids) {
        return baseMapper.batchUpdateState(ids, AgentProfitRecord.STATE_CANCELLED, null) > 0;
    }

    /**
     * 根据支付订单号查询分润记录
     * @param payOrderId 支付订单号
     * @return 分润记录列表
     */
    public List<AgentProfitRecord> getByPayOrderId(String payOrderId) {
        return list(AgentProfitRecord.gw().eq(AgentProfitRecord::getPayOrderId, payOrderId));
    }

    /**
     * 检查支付订单是否已生成分润记录
     * @param agentNo 代理商号
     * @param payOrderId 支付订单号
     * @return 是否已存在
     */
    public boolean existsProfitRecord(String agentNo, String payOrderId) {
        return count(AgentProfitRecord.gw()
                .eq(AgentProfitRecord::getAgentNo, agentNo)
                .eq(AgentProfitRecord::getPayOrderId, payOrderId)) > 0;
    }

    /**
     * 获取代理商今日分润统计
     * @param agentNo 代理商号
     * @return 统计数据
     */
    public Map<String, Object> getTodayProfitStat(String agentNo) {
        Date today = new Date();
        return sumProfitAmount(agentNo, null, today, today);
    }

    /**
     * 获取代理商本月分润统计
     * @param agentNo 代理商号
     * @return 统计数据
     */
    public Map<String, Object> getMonthProfitStat(String agentNo) {
        // 获取本月第一天和最后一天
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.set(java.util.Calendar.DAY_OF_MONTH, 1);
        Date monthStart = cal.getTime();
        
        cal.set(java.util.Calendar.DAY_OF_MONTH, cal.getActualMaximum(java.util.Calendar.DAY_OF_MONTH));
        Date monthEnd = cal.getTime();
        
        return sumProfitAmount(agentNo, null, monthStart, monthEnd);
    }

}
