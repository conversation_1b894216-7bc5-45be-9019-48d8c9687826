/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeequan.jeepay.core.entity.AgentInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 代理商信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AgentInfoMapper extends BaseMapper<AgentInfo> {

    /**
     * 根据代理商路径查询所有下级代理商
     * @param agentPath 代理商路径
     * @return 下级代理商列表
     */
    List<AgentInfo> selectSubAgentsByPath(@Param("agentPath") String agentPath);

    /**
     * 根据上级代理商号查询直属下级代理商
     * @param parentAgentNo 上级代理商号
     * @return 直属下级代理商列表
     */
    List<AgentInfo> selectDirectSubAgents(@Param("parentAgentNo") String parentAgentNo);

    /**
     * 更新代理商路径
     * @param agentNo 代理商号
     * @param agentPath 新的代理商路径
     * @return 更新行数
     */
    int updateAgentPath(@Param("agentNo") String agentNo, @Param("agentPath") String agentPath);

}
