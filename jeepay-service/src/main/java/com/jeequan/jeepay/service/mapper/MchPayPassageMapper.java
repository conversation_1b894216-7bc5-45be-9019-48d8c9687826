/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeequan.jeepay.core.entity.MchPayPassage;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商户支付通道表 Mapper 接口
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2021-04-27
 */
public interface MchPayPassageMapper extends BaseMapper<MchPayPassage> {

    /** 根据支付方式查询可用的支付接口列表 **/
    List<JSONObject> selectAvailablePayInterfaceList(Map params);
}
