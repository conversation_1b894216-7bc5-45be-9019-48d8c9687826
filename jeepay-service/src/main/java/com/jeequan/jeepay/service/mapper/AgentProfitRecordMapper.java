/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeequan.jeepay.core.entity.AgentProfitRecord;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 代理商分润记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AgentProfitRecordMapper extends BaseMapper<AgentProfitRecord> {

    /**
     * 分页查询代理商分润记录
     * @param page 分页参数
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param state 分润状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分润记录分页数据
     */
    IPage<AgentProfitRecord> selectProfitRecordPage(Page<AgentProfitRecord> page,
                                                   @Param("agentNo") String agentNo,
                                                   @Param("mchNo") String mchNo,
                                                   @Param("state") Byte state,
                                                   @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate);

    /**
     * 统计代理商分润金额
     * @param agentNo 代理商号
     * @param state 分润状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分润金额统计
     */
    Map<String, Object> sumProfitAmount(@Param("agentNo") String agentNo,
                                       @Param("state") Byte state,
                                       @Param("startDate") Date startDate,
                                       @Param("endDate") Date endDate);

    /**
     * 批量插入分润记录
     * @param records 分润记录列表
     * @return 插入行数
     */
    int batchInsert(@Param("records") List<AgentProfitRecord> records);

    /**
     * 批量更新分润状态
     * @param ids 记录ID列表
     * @param state 新状态
     * @param settleTime 结算时间
     * @return 更新行数
     */
    int batchUpdateState(@Param("ids") List<Long> ids,
                        @Param("state") Byte state,
                        @Param("settleTime") Date settleTime);

}
