/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeequan.jeepay.core.entity.SysEntitlement;
import org.springframework.stereotype.Service;
import com.jeequan.jeepay.service.mapper.SysEntitlementMapper;

/**
 * <p>
 * 系统权限表 服务实现类
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2020-06-13
 */
@Service
public class SysEntitlementService extends ServiceImpl<SysEntitlementMapper, SysEntitlement> {




}
