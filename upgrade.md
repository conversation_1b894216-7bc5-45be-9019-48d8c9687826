[v1.0.0_20210610]
1. 新增：支持微信服务商和普通商户接口，支持V2和V3接口
2. 新增：支持支付宝服务商和普通商户接口，支持沙箱和生成切换
3. 新增：支持云闪付服务商接口
4. 新增：支持统一下单接口（微信和支付宝所有支付方式）、查单接口、支付回调、获取渠道用户ID
5. 新增：实现聚合码支付，主扫和被扫接口
6. 新增：支持云闪付服务商接口
7. 新增：支持运营平台和商户系统，完善的权限管理

[v1.1.0_20210618]
1. 新增：增加发起退款,查询退款,退款回调接口
2. 新增：增加微信、支付宝、云闪付通道的退款
3. 新增：增加商户多应用管理
4. 新增：增加操作员删除,重置密码功能
5. 新增：增加商户系统操作员删除,重置密码功能
6. 优化：优化支付API接口(商户应用支持)
7. 优化：兼容Mysql8.0版本
8. 优化：优化部分功能数据列表权限
9. 修复：修复一些已知Bug

[v1.1.1_20210625]
1. 新增：商户系统增加支付体验
2. 新增：支付结果使用websocket监听
3. 新增：运营平台支持重发商户通知功能
4. 新增：运营平台完善退款功能(支持全额和部分退款)
5. 新增：java sdk端增加退款实现
6. 优化：解决异步通知编码问题
7. 优化：解决payment项目缓存更新问题
8. 优化：优化退款单号规则
9. 优化：优化部分前端界面UI样式
10. 修复：修改调用上游退款传递订单号问题

[v1.2.0_20210705]
1. 新增：支持通过配置文件切换MQ
2. 新增：增加RabbitMQ的支持
3. 优化：优化微信V3收款,退款接口
4. 优化：优化统计界面退款数据问题
5. 优化：优化部分前端界面UI样式
6. 修复：修复微信V3接口签名问题

[v1.3.0_20210712]
1. 新增：系统支持 RocketMQ 消息中间件
2. 新增：下单接口支持传入expiredTime 订单过期时间参数，单位：秒， 默认两个小时
3. 新增：文件存储支持本地和阿里云OSS，便于扩展 (issue: #I3XV16:扩展oss存储的类型)
4. 修复：解决微信V2接口支付成功上游重复通知问题（#I3ZQWC:重复收到微信支付结果通知)
5. 修复：解决商户通知状态未变化的问题

[v1.4.0_20210719]
1. 新增：系统内服务商子商户的支付宝扫码授权（免录参数）
2. 新增：系统内的参数脱敏处理功能，对敏感数据使用***替代并不可回显仅支持修改
3. 优化：升级springboot版本 2.4.5==》2.4.8（ spring-security <5.4.7 存在安全漏洞：CVE-2021-22119）
4. 优化：删除sun.misc.BASE64Decoder的引用，避免部分环境编译报错的问题；
5. 优化：解决其他已知问题

[v1.5.0_20210727]
1. 新增：提取jeepay-components项目， 放置OSS, MQ公共组件包
2. 优化：重构多MQ的实现，对生产者和消费者项目的使用透明，支持无缝切换三个厂商
3. 优化：ActiveMQ添加账密配置， 添加连接池pool配置
4. 优化：修改聚合码前端项目的提示信息
5. 优化：修改微信支付宝的参数配置项，支持自定义配置页面
6. 修复：修复退款订单查询条件不生效等已知问题

[v1.5.1_20210803]
1. 优化：阿里巴巴代码规范检测整改
2. 优化：sonarLint 代码检测工具检测整改
3. 修复：修复订单页搜索值不对应的BUG

[v1.6.0_20210816]
1. 修复：修复订单多次通知产生异常日志
2. 修复：修复微信条码查单延迟bug
3. 新增：添加微信支付宝的转账接口代码
4. 新增：添加商户发起转账api和查单回调接口
5. 新增：运营平台添加转账订单查询功能
6. 新增：商户系统添加转账订单查询和发起转账功能

[v1.7.0_20210830]
1. 新增：商户系统新增分账组管理、分账关系绑定、分账记录查询功能
2. 新增：支付网关新增分账绑定、分账请求API
3. 新增：支付测试支持分账选项
4. 修复：解决服务商配置修改后没有更新缓存的问题
5. 修复：退款中的订单改为定时任务实时补单
6. 修复 订单表记录表记录商户费率快照: https://gitee.com/jeequan/jeepay/issues/I46MDI
7. 修复：退款订单表记录微信异常信息：https://gitee.com/jeequan/jeepay/issues/I47KUF
8. 修复：主扫被扫都记录上游渠道用户ID: https://gitee.com/jeequan/jeepay/issues/I47KRW

[v1.8.0_20210910]
1. 新增：商户系统添加退款操作并支持功能权限配置
2. 修复：解决URL拼接问题，不再使用hutool拼接函数，改为自行拼接方式； https://gitee.com/jeequan/jeepay/issues/I484PM
3. 优化：微信企业付款到零钱产品不支持服务商模式，在转账时候做限制，避免转出服务商资金 https://gitee.com/jeequan/jeepay/issues/I483FJ

[v1.9.0_20210930]
1. 新增：增加小新支付通道，对接交易和退款接口
2. 新增：增加退款回调处理逻辑
3. 修复：修复云闪付空串导致回调验签失败问题
4. 修复：修复云闪付机构传参问题
5. 优化：支付收银台项目发布在payment项目下
6. 优化：优化支付宝服务商分账接口 https://gitee.com/jeequan/jeepay/issues/I4BNDT

[v1.10.0_20211119]
1. 新增：系统配置、 商户应用、服务商参数配置是否使用内存缓存支持开关式配置
2. 新增：订单号生成支持使用mybatis-plus方式
3. 新增：支付订单全额退款时状态修改为已退款
4. 新增：调整系统的表格密度，一屏显示更多数据
5. 新增：订单列表页支持多合一订单查询和展示
6. 新增：登录页面增加登录验证码默认超时时间，并给予用户反馈
7. 新增：商户系统的支付测试和转账默认一个应用
8. 新增：统一下单接口删除了channelUser字段，统一使用channelExtra传参
9. 修复：角色权限关联表字段扩容，避免入库出现问题； https://gitee.com/jeequan/jeepay/issues/I4DKRL
10. 修复：解决云闪付退款传参不正确导致的问题
11. 修复：修复文件存储位置选择阿里云oss时上传位置错误的问题
12. 修复：解决聚合码更新费率问题 ：https://gitee.com/jeequan/jeepay/issues/I4D2EB
13. 优化：优化支付工具类
14. 优化：微信退款异常添加日志信息
15. 优化：调整接口返回的ContentType: https://gitee.com/jeequan/jeepay/issues/I4H2UX

[v1.11.0_20211223]
1. 新增：增加PayPal支付通道（感谢@青木）
2. 新增：增加阿里云RocketMQ商业版支持（感谢@pimh）
3. 修复：解决fastjson版本导致toJson问题
4. 优化：优化微信子商户的appID和自openID设置问题
5. 优化：优化微信支付证书设置(退款问题)
6. 优化：接口增加String类型处理

[v1.12.0_20220126]
1. 新增：增加微信退款异步回调支持
2. 新增：增加微信H5由payment项目地址统一跳转
3. 新增：增加关闭订单接口（微信、支付宝、云闪付已对接）
4. 新增：增加分账失败重新发起功能转
5. 优化：升级支付宝转账接口（sdk升级到4.22.22.ALL）
6. 修复：修复微信v3接口小程序支付报错问题：https://gitee.com/jeequan/jeepay/issues/I4PJKX
7. 修复：修复安全漏洞（升级mysql-connector-java、velocity-engine-core版本）
8. 修复：修复删除服务商时查看子商户数量问题

[v1.13.0_20220329]
1. 优化：微信和支付宝接口增加支付超时时间：https://gitee.com/jeequan/jeepay/issues/I4WMK0
2. 优化：优化微信支付V3接口对接
3. 优化：项目支持jdk17编译
4. 修复：修复paypal支付查询问题：https://gitee.com/jeequan/jeepay/issues/I4ZOHF
5. 修复：修复微信支付V2接口退款问题
6. 修复：修复支付下单会使用已停用接口问题：https://gitee.com/jeequan/jeepay/issues/I4SM0M

[v1.14.0_20220708]
1. 新增：增加docker的支持
2. 优化：从请求中获取参数并以+=拼接的方式,使用更为优雅的流式拼接
3. 优化：优化商户删除逻辑
4. 优化：分账执行API, 解决提示订单不存在的问题
5. 优化：扩充日志表请求和响应数据的长度
6. 修复：解决因目录不存在导致证书下载失败问题
7. 修复：微信、支付宝设置订单超时bug
8. 修复：OSS保存路径未考虑设置前缀问题
9. 修复：订单关闭传递参数字段与文档描述不一致
10. 修复：微信退款通知检验退款金额问题

[v1.15.0_20220915]
1. 新增：增加计全付(jeepay plus线上支付平台)支付渠道接口
2. 优化：微信H5支持同步跳转
3. 修复：升级fastjson至1.2.83版
4. 修复：修复微信app调起支付签名串问题

[v1.16.0_20221129]
1. 新增：微信转账接口支持V3版本
2. 新增：微信分账接口支持V3版本
3. 优化：优化支付通知回调特殊符号问题
4. 优化：优化微信小程序WX_LITE支付方式判断
5. 修复：修复阿里云oss下载路径问题

[v2.0.0_20230319]
1. 新增：重构支付渠道对接代码，支持接口市场下载安装：https://www.jeequan.com/ifstore/list.html
2. 新增：增加订单创建时重写订单号方法
3. 新增：增加转账订单补单功能
4. 新增：增加微信、支付宝转账查询接口
5. 新增：增加转账异步通知功能
6. 优化：订单回调时回传最新支付数据
7. 优化：优化微信转账、分账完成解冻金额
8. 优化：优化微信商家转账到零钱接口(待正式账号测试)
9. 修复：修复阿里云OSS上传问题

[v2.1.0_20230427]
1. 新增：完善分账结果确认处理机制
2. 新增：增加分账结果异步通知处理
3. 新增：增加分账结果主动查询任务
4. 优化：微信和支付宝分账结果确认
5. 修复：修复微信分账V3接口子商户号取值错误导致无法分账问题

[v2.2.0_20230616]
1. 新增：适配jdk11 17高版本支持（社区）
2. 新增：支持Knife4j文档的适配，添加部分接口示例 http://pay.d.jeepay.vip/doc.html
3. 优化：针对微信V3版本接口进行调整

[v2.2.1_20230816]
1. 优化：更新支付宝沙箱地址
2. 优化：修改数据库时间字段为小数点后3位
3. 优化：搜索条件时间支持毫秒数

[v2.2.2_20231122]
1. 优化：优化微信V3接口APP支付返回参数
2. 优化：优化PayPal支付接口参数名、关单方法名大小写
3. 优化：优化Docker部署
4. 优化：优化分账页面描述

[v2.3.0_20240311]
1. 优化：升级wxjava/hutool到最新版本， 适配最新版本微信支付接口
2. 新增：添加项目下的Dockerfile文件
3. 新增：一键部署安装、卸载脚本
4. 修复： FastJSON开启安全模式，解决安全检测漏洞

[v2.4.0_20240612]
1. 新增：支付宝官方订单码支付方式
2. 修复：分账状态问题 #I97MQM   
3. 修复：解决商户通知转义问题#I9UT23
4. 修复：解决退款直接成功时没有返回 successTime问题
5. 优化：部分情况更新订单异常的现象

[v2.4.1_20250108]
1. 修复：统一下单校验商户应用状态 
2. 修复：优化微信官方退款查询接口的退款逻辑判断；https://gitee.com/jeequan/jeepay/issues/I9RX2D
3. 优化：完善聚合码付款相关业务
4. 优化：selectOrderCount查询一次即可。 https://gitee.com/jeequan/jeepay/issues/IANUTB
5. 优化：更新activemq到5.16.7
6. 优化：优化AliYunRocketMQ的配置和消息组管理命名

[v3.0.0_20250109]
1. 新增： 升级编译代码到jdk17
2. 新增： 升级springboot到3.3.7
3. 新增： 升级Knife4j和兼容历史swagger api 
4. 优化： 新建商户后不支持更改服务商  https://gitee.com/jeequan/jeepay/issues/IBF9D9
5. 修复： 解决已知问题

[v3.1.0_20250314]
1. 新增： 支持微信公钥模式
2. 新增： 新版微信转账的接入 公告： https://developers.weixin.qq.com/community/pay/doc/000a060bb4c13095b6b27cc1b6ac01?page=1
3. 优化： 支付测试自动关闭流程 
4. 修复： 解决已知问题

