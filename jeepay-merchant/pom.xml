<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion> <!-- POM模型版本 -->

    <groupId>com.jeequan</groupId> <!-- 组织名, 类似于包名 -->
    <artifactId>jeepay-merchant</artifactId>  <!-- 项目名称 -->
    <packaging>jar</packaging> <!-- 项目的最终打包类型/发布形式, 可选[jar, war, pom, maven-plugin]等 -->
    <version>${isys.version}</version> <!-- 项目当前版本号 -->
    <description>四方支付系统 [商户后台管理端]</description> <!-- 项目描述 -->
    <url>https://www.jeequan.com</url>

    <parent>
        <groupId>com.jeequan</groupId>
        <artifactId>jeepay</artifactId>
        <version>Final</version>
    </parent>
    <!-- 项目依赖声明 -->
    <dependencies>

        <!-- 依赖[ service ]包, 会自动传递依赖[ core ]包。  -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-service</artifactId>
        </dependency>

        <!-- 依赖[ oss ]包  -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-components-oss</artifactId>
        </dependency>

        <!-- 依赖[ mq ]包  -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-components-mq</artifactId>
        </dependency>

        <!-- 依赖 sping-boot-web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>  <!-- 删除spring boot默认json映射器： Jackson， 引入fastJSON   -->
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jdk8</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.module</groupId>
                    <artifactId>jackson-module-parameter-names</artifactId>
                </exclusion>
                <exclusion>  <!-- hibernate.validator插件一般不使用 -->
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- spring-security  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!-- spring-aop -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- JWT  -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>

        <!-- freemarker -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- 添加redis支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- 引入 jeepay-sdk-java -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-sdk-java</artifactId>
        </dependency>

        <!-- webSocket -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
        </dependency>

        <!-- Knife4j  -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>

    <!-- 作为可执行jar -->
    <build>
        <finalName>${project.artifactId}</finalName>

        <!-- resources资源配置项 -->
        <resources>
            <!-- 通用资源文件 -->
            <resource><directory>src/main/resources</directory><includes><include>**/*.*</include></includes></resource>

            <!-- 放置通用配置yml文件， 开发时仅配置一套参数即可。   实际生产环境下应在每个项目下 与jar同级目录下新建application.yml覆写对应参数。  -->
            <resource>
                <directory>../conf/devCommons</directory>
                <includes><include>**/*.yml</include></includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <configuration>
                    <container>
                        <entrypoint>
                            sh,-c,java $JVM_OPTS -cp $( cat /app/jib-classpath-file ) $( cat /app/jib-main-class-file ) $JAVA_ARGS
                        </entrypoint>
                    </container>
                </configuration>
            </plugin>

        </plugins>
    </build>


</project>
