{"name": "jeepay-ui-cashier", "version": "1.10.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^0.19.0", "core-js": "^3.6.5", "lib-flexible": "^0.3.2", "postcss-px2rem": "^0.3.0", "px2rem-loader": "^0.1.9", "vue": "^2.6.11", "vue-router": "^3.2.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.13", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}