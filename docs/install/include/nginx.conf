
# 官方默认配置文件， 下载地址： https://nginx.org/download/nginx-1.18.0.tar.gz
#user  nobody;
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    # 运营平台
    server {
        listen       19217; #监听端口，若监听域名请添加 server_name
        root /usr/share/nginx/html/jeepaymanager;
        index index.html index.htm;

        #解决vue刷新404问题
        try_files $uri $uri/ /index.html;

        # 主页面不允许缓存（避免项目升级 空白页的问题）
        location /index.html {
          add_header Expires -1;
          add_header Cache-Control no-cache;
        }

        location /api/
        {
            proxy_next_upstream http_502 http_504 error timeout invalid_header;
            proxy_set_header Host  $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://localhost:9217;
            # 启用支持websocket连接
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }


    # 商户平台
    server {
        listen       19218; #监听端口，若监听域名请添加 server_name
        root /usr/share/nginx/html/jeepaymerchant;
        index index.html index.htm;

        #解决vue刷新404问题
        try_files $uri $uri/ /index.html;

        # 主页面不允许缓存（避免项目升级 空白页的问题）
        location /index.html {
          add_header Expires -1;
          add_header Cache-Control no-cache;
        }

        location /api/
        {
            proxy_next_upstream http_502 http_504 error timeout invalid_header;
            proxy_set_header Host  $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://localhost:9218;
            # 启用支持websocket连接
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }


    # 支付网关
    server {
        listen       19216; #监听端口，若监听域名请添加 server_name
        location /
        {
            proxy_next_upstream http_502 http_504 error timeout invalid_header;
            proxy_set_header Host  $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://localhost:9216;
        }
    }

}
