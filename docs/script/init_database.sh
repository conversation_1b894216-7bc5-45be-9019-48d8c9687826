#!/bin/bash

# 脚本用于创建MySQL数据库、用户并执行初始化SQL
# 作者：AI助手
# 日期：2025-07-02

echo "开始创建MySQL数据库和用户..."

# MySQL连接信息
MYSQL_HOST="127.0.0.1"
MYSQL_PORT="3307"
MYSQL_ROOT_USER="root"
MYSQL_ROOT_PASSWORD=""

# 要创建的数据库和用户信息
DB_NAME="jeepaydb"
DB_USER="jeepay"
DB_PASSWORD="123456"

# SQL文件路径
SQL_FILE="/home/<USER>/jeepay-master/docs/sql/init.sql"

# 检查SQL文件是否存在
if [ ! -f "$SQL_FILE" ]; then
    echo "错误：SQL文件 $SQL_FILE 不存在！"
    exit 1
fi

# 创建数据库和用户
echo "创建数据库 $DB_NAME 和用户 $DB_USER..."

# 连接MySQL并创建数据库和用户
mysql -h$MYSQL_HOST -P$MYSQL_PORT -u$MYSQL_ROOT_USER -p$MYSQL_ROOT_PASSWORD << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'%';
FLUSH PRIVILEGES;
EOF

if [ $? -ne 0 ]; then
    echo "错误：创建数据库或用户失败！"
    exit 1
fi

echo "数据库和用户创建成功！"

# 执行SQL初始化文件
echo "执行SQL初始化文件..."
mysql -h$MYSQL_HOST -P$MYSQL_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME < $SQL_FILE

if [ $? -ne 0 ]; then
    echo "错误：执行SQL文件失败！"
    exit 1
fi

echo "SQL初始化文件执行成功！"
echo "数据库初始化完成！"