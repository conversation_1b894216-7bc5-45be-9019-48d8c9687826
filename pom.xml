<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> <!-- POM模型版本 -->

  <groupId>com.jeequan</groupId> <!-- 组织名, 类似于包名 -->
  <artifactId>jeepay</artifactId>  <!-- 项目名称  -->
  <packaging>pom</packaging> <!-- 项目的最终打包类型/发布形式, 可选[jar, war, pom, maven-plugin]等 -->

  <name>jeepay</name>
  <version>Final</version> <!-- pom版本号/项目总版本号， 每个子项目引入的版本号必须一致。  最外层的pom.xml版本号保持不变，始终为Final版本。 更新版本请更改isys.version属性  -->
  <description>四方支付系统</description> <!-- 项目描述 -->
  <url>https://www.jeequan.com</url>

  <!-- 继承：Spring Boot Parent -->
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.3.7</version>
  </parent>

  <!-- 声明子项目 -->
  <modules>
    <module>jeepay-z-codegen</module>    <!-- 代码生成器 -->

    <module>jeepay-core</module>  <!-- 基础函数, 包含工具类等 -->
    <module>jeepay-service</module>  <!-- db service等 -->

  	<module>jeepay-manager</module>	<!-- 运营平台管理端 -->
  	<module>jeepay-merchant</module>	<!-- 商户平台管理端 -->
  	<module>jeepay-agent</module>	<!-- 代理商平台管理端 -->
    <module>jeepay-payment</module>	<!-- 支付统一网关 -->

    <module>jeepay-components</module> <!-- 组件包 -->
  </modules>

  <!-- 配置属性声明, 支持自定义参数 -->
  <properties>

    <isys.version>3.0.0</isys.version> <!-- 指定当前[项目]版本号 -->
    <projectRootDir>${basedir}</projectRootDir>
    <java.version>17</java.version> <!-- 指定java版本号 -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding> <!-- 项目构建输出编码 -->

    <!-- 其他工具包 -->
    <jeepay.sdk.java.version>1.6.1</jeepay.sdk.java.version>
    <fastjson.version>1.2.83</fastjson.version> <!-- fastjson -->
    <mybatis.plus.starter.version>3.5.7</mybatis.plus.starter.version>  <!-- mybatis plus -->
    <hutool.util.version>5.8.26</hutool.util.version>  <!-- hutool -->
    <jjwt.version>0.9.1</jjwt.version>
    <binarywang.weixin.java.version>4.7.2.B</binarywang.weixin.java.version>
    <rocketmq.spring.boot.starter.version>2.2.0</rocketmq.spring.boot.starter.version>
    <aliyun-openservices-ons-client.version>1.8.8.5.Final</aliyun-openservices-ons-client.version>
    <javax.annotation-api.version>1.3.2</javax.annotation-api.version>

    <mysql.version>8.0.28</mysql.version> <!-- 覆写 spring-boot-dependencies 的依赖版本号 -->
    <mysql-connector-j.version>9.1.0</mysql-connector-j.version>
    <jaxb-api.version>2.3.0</jaxb-api.version>

  </properties>


  <!-- 依赖包管理， 按需添加 -->
  <dependencyManagement>
    <dependencies>

        <!-- 依赖 [ core ]包。  -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-core</artifactId>
            <version>${isys.version}</version>
        </dependency>

        <!-- [ service ]包, 会自动传递依赖[ core ]包。  -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-service</artifactId>
            <version>${isys.version}</version>
        </dependency>

        <!-- [ oss ]包  -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-components-oss</artifactId>
            <version>${isys.version}</version>
        </dependency>

        <!-- [ mq ]包  -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-components-mq</artifactId>
            <version>${isys.version}</version>
        </dependency>

        <!-- jeepay sdk 支付工具包 -->
        <dependency>
            <groupId>com.jeequan</groupId>
            <artifactId>jeepay-sdk-java</artifactId>
            <version>${jeepay.sdk.java.version}</version>
        </dependency>

        <!-- alibaba FastJSON -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!-- JWT  -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jjwt.version}</version>
        </dependency>

        <!--wx_pay  https://github.com/wechat-group/WxJava  -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
            <version>${binarywang.weixin.java.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>${binarywang.weixin.java.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis.plus.starter.version}</version>
        </dependency>

        <!-- MySql 数据库连接包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql-connector-j.version}</version>
        </dependency>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>5.0.0</version>
        </dependency>

        <!-- 生成二维码依赖 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.1.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alipay.sdk/alipay-sdk-java -->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.38.61.ALL</version>
        </dependency>

        <!-- 阿里云oss组件  -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.13.0</version>
        </dependency>

        <!-- 添加对rocketMQ的支持  -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>${rocketmq.spring.boot.starter.version}</version>
        </dependency>

        <!-- 添加对AliyunRocketMQ的支持 -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <version>${aliyun-openservices-ons-client.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>${jaxb-api.version}</version>
        </dependency>


        <!-- Knife4j  -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <version>4.5.0</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-models-jakarta</artifactId>
            <version>2.2.21</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>8.0.1.Final</version>
        </dependency>
    </dependencies>

  </dependencyManagement>

  <!-- 所有项目 项目依赖声明 -->
  <dependencies>

      <dependency>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
          <optional>true</optional>
          <scope>provided</scope> <!-- 编译阶段生效，不需要打入包中 -->
      </dependency>

      <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-all</artifactId>
          <version>${hutool.util.version}</version>
      </dependency>
  </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>


</project>
