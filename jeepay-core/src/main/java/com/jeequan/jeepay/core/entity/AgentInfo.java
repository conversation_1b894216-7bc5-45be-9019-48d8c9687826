/*
 * Copyright (c) 2021-2031, 河北计全科技有限公司 (https://www.jeequan.com & <EMAIL>).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jeequan.jeepay.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jeequan.jeepay.core.model.BaseModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 代理商信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "代理商信息表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_agent_info")
public class AgentInfo extends BaseModel implements Serializable {

    public static final LambdaQueryWrapper<AgentInfo> gw(){
        return new LambdaQueryWrapper<>();
    }

    private static final long serialVersionUID = 1L;

    // 代理商状态
    public static final byte STATE_STOP = 0; //停用
    public static final byte STATE_NORMAL = 1; //正常

    // 代理商类型
    public static final byte TYPE_LEVEL_1 = 1; //一级代理商
    public static final byte TYPE_LEVEL_2 = 2; //二级代理商
    public static final byte TYPE_LEVEL_3 = 3; //三级代理商

    /**
     * 代理商号
     */
    @Schema(title = "agentNo", description = "代理商号")
    @TableId
    private String agentNo;

    /**
     * 代理商名称
     */
    @Schema(title = "agentName", description = "代理商名称")
    private String agentName;

    /**
     * 代理商简称
     */
    @Schema(title = "agentShortName", description = "代理商简称")
    private String agentShortName;

    /**
     * 代理商类型: 1-一级代理商, 2-二级代理商, 3-三级代理商等
     */
    @Schema(title = "agentType", description = "代理商类型: 1-一级代理商, 2-二级代理商, 3-三级代理商等")
    private Byte agentType;

    /**
     * 上级代理商号
     */
    @Schema(title = "parentAgentNo", description = "上级代理商号")
    private String parentAgentNo;

    /**
     * 代理商层级: 1-一级, 2-二级, 3-三级等
     */
    @Schema(title = "agentLevel", description = "代理商层级: 1-一级, 2-二级, 3-三级等")
    private Byte agentLevel;

    /**
     * 代理商层级路径，如: /A001/A002/A003
     */
    @Schema(title = "agentPath", description = "代理商层级路径，如: /A001/A002/A003")
    private String agentPath;

    /**
     * 联系人姓名
     */
    @Schema(title = "contactName", description = "联系人姓名")
    private String contactName;

    /**
     * 联系人手机号
     */
    @Schema(title = "contactTel", description = "联系人手机号")
    private String contactTel;

    /**
     * 联系人邮箱
     */
    @Schema(title = "contactEmail", description = "联系人邮箱")
    private String contactEmail;

    /**
     * 省份
     */
    @Schema(title = "province", description = "省份")
    private String province;

    /**
     * 城市
     */
    @Schema(title = "city", description = "城市")
    private String city;

    /**
     * 区县
     */
    @Schema(title = "district", description = "区县")
    private String district;

    /**
     * 详细地址
     */
    @Schema(title = "address", description = "详细地址")
    private String address;

    /**
     * 代理商分润比例
     */
    @Schema(title = "profitRate", description = "代理商分润比例")
    private BigDecimal profitRate;

    /**
     * 代理商状态: 0-停用, 1-正常
     */
    @Schema(title = "state", description = "代理商状态: 0-停用, 1-正常")
    private Byte state;

    /**
     * 代理商备注
     */
    @Schema(title = "remark", description = "代理商备注")
    private String remark;

    /**
     * 初始用户ID（创建代理商时，允许代理商登录的用户）
     */
    @Schema(title = "initUserId", description = "初始用户ID（创建代理商时，允许代理商登录的用户）")
    private Long initUserId;

    /**
     * 创建者用户ID
     */
    @Schema(title = "createdUid", description = "创建者用户ID")
    private Long createdUid;

    /**
     * 创建者姓名
     */
    @Schema(title = "createdBy", description = "创建者姓名")
    private String createdBy;

    /**
     * 创建时间
     */
    @Schema(title = "createdAt", description = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Schema(title = "updatedAt", description = "更新时间")
    private Date updatedAt;

}
